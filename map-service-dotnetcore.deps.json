{"runtimeTarget": {"name": ".NETCoreApp,Version=v6.0", "signature": ""}, "compilationOptions": {}, "targets": {".NETCoreApp,Version=v6.0": {"map-service-dotnetcore/1.0.0": {"dependencies": {"ECS-dotnetcore": "1.0.0", "Swashbuckle.AspNetCore": "6.2.3"}, "runtime": {"map-service-dotnetcore.dll": {}}}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {}, "Microsoft.OpenApi/1.2.3": {"runtime": {"lib/netstandard2.0/Microsoft.OpenApi.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "SkiaSharp/2.88.2": {"dependencies": {"SkiaSharp.NativeAssets.Win32": "2.88.2", "SkiaSharp.NativeAssets.macOS": "2.88.2"}, "runtime": {"lib/net6.0/SkiaSharp.dll": {"assemblyVersion": "********", "fileVersion": "********"}}}, "SkiaSharp.NativeAssets.Linux.NoDependencies/2.88.2": {"dependencies": {"SkiaSharp": "2.88.2"}, "runtimeTargets": {"runtimes/linux-arm/native/libSkiaSharp.so": {"rid": "linux-arm", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-arm64/native/libSkiaSharp.so": {"rid": "linux-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-musl-x64/native/libSkiaSharp.so": {"rid": "linux-musl-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/linux-x64/native/libSkiaSharp.so": {"rid": "linux-x64", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.macOS/2.88.2": {"runtimeTargets": {"runtimes/osx/native/libSkiaSharp.dylib": {"rid": "osx", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "SkiaSharp.NativeAssets.Win32/2.88.2": {"runtimeTargets": {"runtimes/win-arm64/native/libSkiaSharp.dll": {"rid": "win-arm64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x64/native/libSkiaSharp.dll": {"rid": "win-x64", "assetType": "native", "fileVersion": "0.0.0.0"}, "runtimes/win-x86/native/libSkiaSharp.dll": {"rid": "win-x86", "assetType": "native", "fileVersion": "0.0.0.0"}}}, "Swashbuckle.AspNetCore/6.2.3": {"dependencies": {"Microsoft.Extensions.ApiDescription.Server": "3.0.0", "Swashbuckle.AspNetCore.Swagger": "6.2.3", "Swashbuckle.AspNetCore.SwaggerGen": "6.2.3", "Swashbuckle.AspNetCore.SwaggerUI": "6.2.3"}}, "Swashbuckle.AspNetCore.Swagger/6.2.3": {"dependencies": {"Microsoft.OpenApi": "1.2.3"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.Swagger.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerGen/6.2.3": {"dependencies": {"Swashbuckle.AspNetCore.Swagger": "6.2.3"}, "runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerGen.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "Swashbuckle.AspNetCore.SwaggerUI/6.2.3": {"runtime": {"lib/net6.0/Swashbuckle.AspNetCore.SwaggerUI.dll": {"assemblyVersion": "*******", "fileVersion": "*******"}}}, "System.Resources.Extensions/5.0.0": {"runtime": {"lib/netstandard2.0/System.Resources.Extensions.dll": {"assemblyVersion": "*******", "fileVersion": "5.0.20.51904"}}}, "ECS-dotnetcore/1.0.0": {"dependencies": {"SkiaSharp.NativeAssets.Linux.NoDependencies": "2.88.2", "System.Resources.Extensions": "5.0.0"}, "runtime": {"ECS-dotnetcore.dll": {}}}}}, "libraries": {"map-service-dotnetcore/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}, "Microsoft.Extensions.ApiDescription.Server/3.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-LH4OE/76F6sOCslif7+Xh3fS/wUUrE5ryeXAMcoCnuwOQGT5Smw0p57IgDh/pHgHaGz/e+AmEQb7pRgb++wt0w==", "path": "microsoft.extensions.apidescription.server/3.0.0", "hashPath": "microsoft.extensions.apidescription.server.3.0.0.nupkg.sha512"}, "Microsoft.OpenApi/1.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-Nug3rO+7Kl5/SBAadzSMAVgqDlfGjJZ0GenQrLywJ84XGKO0uRqkunz5Wyl0SDwcR71bAATXvSdbdzPrYRYKGw==", "path": "microsoft.openapi/1.2.3", "hashPath": "microsoft.openapi.1.2.3.nupkg.sha512"}, "SkiaSharp/2.88.2": {"type": "package", "serviceable": true, "sha512": "sha512-B7ToGj6WO4K4iEtD1Lydrt5FARxMNbNjnlEa4M2TaKs3EM0cmZLrJjCaZvfOJL9gXkswJb5pTSJL9Qz9AHd+Jw==", "path": "skiasharp/2.88.2", "hashPath": "skiasharp.2.88.2.nupkg.sha512"}, "SkiaSharp.NativeAssets.Linux.NoDependencies/2.88.2": {"type": "package", "serviceable": true, "sha512": "sha512-LLRwQ/aZHlnhYuFoeycvUnz/iex1xcWWF4nvDKv2ctiVAMqVOjPxu7J6VZAEGCC3zgGi+eM7Ky59+bvHf0X5+g==", "path": "skiasharp.nativeassets.linux.nodependencies/2.88.2", "hashPath": "skiasharp.nativeassets.linux.nodependencies.2.88.2.nupkg.sha512"}, "SkiaSharp.NativeAssets.macOS/2.88.2": {"type": "package", "serviceable": true, "sha512": "sha512-mUtY9w9KP6LnhE3VgwE331ttOclx239MENCo1aPT27AJX6QkDpeeqSK83/Rggu3NYPkUwIdg6fU1MOBdv2as4A==", "path": "skiasharp.nativeassets.macos/2.88.2", "hashPath": "skiasharp.nativeassets.macos.2.88.2.nupkg.sha512"}, "SkiaSharp.NativeAssets.Win32/2.88.2": {"type": "package", "serviceable": true, "sha512": "sha512-FgUHWpnfLcFiqAXuJEgcvUn5p7gHwkkfclWFU8LKfydUzwJyq+izWxL5OCVgbB8wYrX/TSx42uh8LwstEAURzw==", "path": "skiasharp.nativeassets.win32/2.88.2", "hashPath": "skiasharp.nativeassets.win32.2.88.2.nupkg.sha512"}, "Swashbuckle.AspNetCore/6.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-cnzQDn0Le+hInsw2SYwlOhOCPXpYi/szcvnyqZJ12v+QyrLBwAmWXBg6RIyHB18s/mLeywC+Rg2O9ndz0IUNYQ==", "path": "swashbuckle.aspnetcore/6.2.3", "hashPath": "swashbuckle.aspnetcore.6.2.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.Swagger/6.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-qOF7j1sL0bWm8g/qqHVPCvkO3JlVvUIB8WfC98kSh6BT5y5DAnBNctfac7XR5EZf+eD7/WasvANncTqwZYfmWQ==", "path": "swashbuckle.aspnetcore.swagger/6.2.3", "hashPath": "swashbuckle.aspnetcore.swagger.6.2.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerGen/6.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-+Xq7WdMCCfcXlnbLJVFNgY8ITdP2TRYIlpbt6IKzDw5FwFxdi9lBfNDtcT+/wkKwX70iBBFmXldnnd02/VO72A==", "path": "swashbuckle.aspnetcore.swaggergen/6.2.3", "hashPath": "swashbuckle.aspnetcore.swaggergen.6.2.3.nupkg.sha512"}, "Swashbuckle.AspNetCore.SwaggerUI/6.2.3": {"type": "package", "serviceable": true, "sha512": "sha512-bCRI87uKJVb4G+KURWm8LQrL64St04dEFZcF6gIM67Zc0Sr/N47EO83ybLMYOvfNdO1DCv8xwPcrz9J/VEhQ5g==", "path": "swashbuckle.aspnetcore.swaggerui/6.2.3", "hashPath": "swashbuckle.aspnetcore.swaggerui.6.2.3.nupkg.sha512"}, "System.Resources.Extensions/5.0.0": {"type": "package", "serviceable": true, "sha512": "sha512-KBiqY/+W6hWVwucHBHO76JkgBGyawlxCcP946MhK8dNjalaBgZkyHaux8ko58PENTqQHHjoDa7cfIkAchfJPPA==", "path": "system.resources.extensions/5.0.0", "hashPath": "system.resources.extensions.5.0.0.nupkg.sha512"}, "ECS-dotnetcore/1.0.0": {"type": "project", "serviceable": false, "sha512": ""}}}