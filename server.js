const express = require('express');
const cors = require('cors');
const path = require('path');
const fs = require('fs-extra');
const axios = require('axios');
const sharp = require('sharp');
const config = require('./config.json');

const app = express();
const PORT = config.server.port || 3000;

// 中间件
app.use(cors());
app.use(express.json());
app.use(express.static('public'));

// 日志中间件
app.use((req, res, next) => {
    console.log(`${new Date().toISOString()} - ${req.method} ${req.url}`);
    next();
});

// 瓦片服务类
class TileService {
    constructor() {
        this.cacheFolder = config.cache.folder;
        this.ensureCacheFolder();
    }

    // 确保缓存文件夹存在
    async ensureCacheFolder() {
        try {
            await fs.ensureDir(this.cacheFolder);
        } catch (error) {
            console.error('创建缓存文件夹失败:', error);
        }
    }

    // 生成缓存文件路径
    getCachePath(x, y, z, cm, dm, dpt) {
        // 根据观察到的缓存结构：z/x/y/z-x-y-cmdmdpt.png
        const filename = `${z}-${x}-${y}-${cm}${dm}${dpt}.png`;
        return path.join(this.cacheFolder, z.toString(), x.toString(), y.toString(), filename);
    }

    // 检查缓存文件是否存在
    async isCacheExists(cachePath) {
        try {
            await fs.access(cachePath);
            return true;
        } catch {
            return false;
        }
    }

    // 从缓存获取瓦片
    async getTileFromCache(cachePath) {
        try {
            return await fs.readFile(cachePath);
        } catch (error) {
            console.error('读取缓存文件失败:', error);
            return null;
        }
    }

    // 从外部服务获取瓦片
    async getTileFromService(x, y, z) {
        try {
            const serviceUrl = config.mapServices.DTMapService;
            if (!serviceUrl) {
                throw new Error('未配置地图服务URL');
            }

            // 替换URL中的占位符
            const url = serviceUrl
                .replace('{x}', x)
                .replace('{y}', y)
                .replace('{z}', z);

            console.log('从外部服务获取瓦片:', url);

            const response = await axios.get(url, {
                responseType: 'arraybuffer',
                timeout: 10000
            });

            return Buffer.from(response.data);
        } catch (error) {
            console.error('从外部服务获取瓦片失败:', error);
            return null;
        }
    }

    // 保存瓦片到缓存
    async saveTileToCache(cachePath, tileData) {
        try {
            await fs.ensureDir(path.dirname(cachePath));
            await fs.writeFile(cachePath, tileData);
            console.log('瓦片已保存到缓存:', cachePath);
        } catch (error) {
            console.error('保存瓦片到缓存失败:', error);
        }
    }

    // 生成空白瓦片
    async generateBlankTile() {
        try {
            return await sharp({
                create: {
                    width: 256,
                    height: 256,
                    channels: 4,
                    background: { r: 255, g: 255, b: 255, alpha: 0 }
                }
            })
            .png()
            .toBuffer();
        } catch (error) {
            console.error('生成空白瓦片失败:', error);
            return null;
        }
    }

    // 主要的瓦片获取方法
    async getTile(x, y, z, cm = 1, dm = 0, dpt = 1) {
        const cachePath = this.getCachePath(x, y, z, cm, dm, dpt);
        
        // 首先检查缓存
        if (await this.isCacheExists(cachePath)) {
            console.log('从缓存获取瓦片:', cachePath);
            return await this.getTileFromCache(cachePath);
        }

        // 缓存中没有，尝试从外部服务获取
        let tileData = await this.getTileFromService(x, y, z);
        
        if (tileData) {
            // 保存到缓存
            await this.saveTileToCache(cachePath, tileData);
            return tileData;
        }

        // 如果都失败了，返回空白瓦片
        console.log('生成空白瓦片');
        return await this.generateBlankTile();
    }
}

// 创建瓦片服务实例
const tileService = new TileService();

// 瓦片请求处理路由
app.get('/Tile.ashx', async (req, res) => {
    try {
        const { x, y, z, cm = 1, dm = 0, dpt = 1 } = req.query;

        // 参数验证
        if (!x || !y || !z) {
            return res.status(400).json({ 
                error: '缺少必需参数: x, y, z' 
            });
        }

        const xNum = parseInt(x);
        const yNum = parseInt(y);
        const zNum = parseInt(z);
        const cmNum = parseInt(cm);
        const dmNum = parseInt(dm);
        const dptNum = parseInt(dpt);

        // 参数范围验证
        if (isNaN(xNum) || isNaN(yNum) || isNaN(zNum) || 
            xNum < 0 || yNum < 0 || zNum < 0 || zNum > 20) {
            return res.status(400).json({ 
                error: '参数值无效' 
            });
        }

        console.log(`请求瓦片: x=${xNum}, y=${yNum}, z=${zNum}, cm=${cmNum}, dm=${dmNum}, dpt=${dptNum}`);

        // 获取瓦片数据
        const tileData = await tileService.getTile(xNum, yNum, zNum, cmNum, dmNum, dptNum);

        if (!tileData) {
            return res.status(404).json({ 
                error: '瓦片不存在' 
            });
        }

        // 设置响应头
        res.set({
            'Content-Type': 'image/png',
            'Cache-Control': 'public, max-age=86400', // 缓存1天
            'Access-Control-Allow-Origin': '*'
        });

        res.send(tileData);

    } catch (error) {
        console.error('处理瓦片请求失败:', error);
        res.status(500).json({ 
            error: '服务器内部错误' 
        });
    }
});

// 健康检查路由
app.get('/health', (req, res) => {
    res.json({ 
        status: 'ok', 
        timestamp: new Date().toISOString(),
        service: 'Map Tile Service'
    });
});

// 服务信息路由
app.get('/info', (req, res) => {
    res.json({
        name: 'Map Tile Service (Node.js)',
        version: '1.0.0',
        description: '复刻 .NET 版本的瓦片服务',
        endpoints: {
            tile: '/Tile.ashx?x={x}&y={y}&z={z}&cm={cm}&dm={dm}&dpt={dpt}',
            health: '/health',
            info: '/info'
        },
        config: {
            cacheEnabled: config.cache.enabled,
            cacheFolder: config.cache.folder
        }
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log(`瓦片服务已启动`);
    console.log(`服务地址: http://localhost:${PORT}`);
    console.log(`瓦片URL: http://localhost:${PORT}/Tile.ashx?x={x}&y={y}&z={z}&cm=1&dm=0&dpt=1`);
    console.log(`健康检查: http://localhost:${PORT}/health`);
    console.log(`服务信息: http://localhost:${PORT}/info`);
});

// 优雅关闭
process.on('SIGTERM', () => {
    console.log('收到 SIGTERM 信号，正在关闭服务器...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('收到 SIGINT 信号，正在关闭服务器...');
    process.exit(0);
});

module.exports = app;
