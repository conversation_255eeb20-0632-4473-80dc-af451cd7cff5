const request = require('supertest');
const app = require('./server');

describe('瓦片服务测试', () => {
    test('健康检查应该返回 OK', async () => {
        const response = await request(app)
            .get('/health')
            .expect(200);
        
        expect(response.body.status).toBe('ok');
        expect(response.body.service).toBe('Map Tile Service');
    });

    test('服务信息应该返回正确的信息', async () => {
        const response = await request(app)
            .get('/info')
            .expect(200);
        
        expect(response.body.name).toBe('Map Tile Service (Node.js)');
        expect(response.body.version).toBe('1.0.0');
        expect(response.body.endpoints).toBeDefined();
    });

    test('瓦片请求缺少参数应该返回 400', async () => {
        const response = await request(app)
            .get('/Tile.ashx')
            .expect(400);
        
        expect(response.body.error).toContain('缺少必需参数');
    });

    test('瓦片请求参数无效应该返回 400', async () => {
        const response = await request(app)
            .get('/Tile.ashx?x=invalid&y=1&z=1')
            .expect(400);
        
        expect(response.body.error).toContain('参数值无效');
    });

    test('有效的瓦片请求应该返回图片或404', async () => {
        const response = await request(app)
            .get('/Tile.ashx?x=1&y=1&z=1&cm=1&dm=0&dpt=1');
        
        // 应该返回 200（找到瓦片）或 404（瓦片不存在）
        expect([200, 404]).toContain(response.status);
        
        if (response.status === 200) {
            expect(response.headers['content-type']).toBe('image/png');
        }
    });
});

describe('瓦片服务类测试', () => {
    const TileService = require('./server').TileService;
    
    test('缓存路径生成应该正确', () => {
        // 这个测试需要导出 TileService 类
        // 暂时跳过，因为当前实现没有导出类
    });
});
