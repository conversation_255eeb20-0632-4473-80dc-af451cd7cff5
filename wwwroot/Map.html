﻿<!DOCTYPE html>
<html>
<head>
    <link rel="stylesheet" href="leaflet/leaflet.css" />
    <script src="leaflet/leaflet.js"></script>
    <meta charset="utf-8" />
    <title></title>
</head>
<body>
    <div id="map" style="width:1200px;height:900px"></div>
    <script type="text/javascript">
        var map = L.map('map').setView([31.9788, 121.70226], 13);
        //var mapUrl = 'http://localhost:57402/MapService/Tile.ashx?x={x}&y={y}&z={z}&cm=1&dm=0&dpt=1'
        var mapUrl = './Tile.ashx?x={x}&y={y}&z={z}&cm=1&dm=0&dpt=1'
        L.tileLayer(mapUrl, {
            attribution: ''
        }).addTo(map);


    </script>
</body>
</html>