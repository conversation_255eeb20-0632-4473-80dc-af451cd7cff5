<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>Node.js 瓦片服务测试</title>
    <link rel="stylesheet" href="https://unpkg.com/leaflet@1.9.3/dist/leaflet.css" />
    <script src="https://unpkg.com/leaflet@1.9.3/dist/leaflet.js"></script>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        #map {
            width: 100%;
            height: 600px;
            border: 1px solid #ccc;
        }
        .info {
            margin-bottom: 20px;
            padding: 10px;
            background-color: #f5f5f5;
            border-radius: 5px;
        }
        .controls {
            margin-top: 20px;
            padding: 10px;
            background-color: #e8f4f8;
            border-radius: 5px;
        }
        button {
            margin: 5px;
            padding: 8px 16px;
            background-color: #007cba;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background-color: #005a87;
        }
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .status.success {
            background-color: #d4edda;
            color: #155724;
        }
        .status.error {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="info">
        <h1>Node.js 瓦片服务测试页面</h1>
        <p>这个页面用于测试 Node.js Express 瓦片服务，复刻 .NET 版本的功能。</p>
        <p><strong>瓦片服务URL:</strong> <code>http://localhost:3000/Tile.ashx?x={x}&y={y}&z={z}&cm=1&dm=0&dpt=1</code></p>
    </div>

    <div id="map"></div>

    <div class="controls">
        <h3>测试控制</h3>
        <button onclick="testTileService()">测试瓦片服务</button>
        <button onclick="testHealthCheck()">健康检查</button>
        <button onclick="testServiceInfo()">服务信息</button>
        <button onclick="refreshMap()">刷新地图</button>
        <div id="status" class="status" style="display: none;"></div>
    </div>

    <script>
        // 初始化地图
        var map = L.map('map').setView([31.9788, 121.70226], 13);

        // 添加瓦片图层
        var tileLayer = L.tileLayer('http://localhost:3000/Tile.ashx?x={x}&y={y}&z={z}&cm=1&dm=0&dpt=1', {
            attribution: 'Node.js 瓦片服务',
            maxZoom: 18,
            tileSize: 256
        });

        // 添加备用图层（OpenStreetMap）
        var osmLayer = L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
            attribution: '© OpenStreetMap contributors'
        });

        // 默认添加我们的瓦片图层
        tileLayer.addTo(map);

        // 添加图层控制
        var baseMaps = {
            "Node.js 瓦片服务": tileLayer,
            "OpenStreetMap": osmLayer
        };
        L.control.layers(baseMaps).addTo(map);

        // 显示状态信息
        function showStatus(message, isError = false) {
            const statusDiv = document.getElementById('status');
            statusDiv.textContent = message;
            statusDiv.className = 'status ' + (isError ? 'error' : 'success');
            statusDiv.style.display = 'block';
            
            // 3秒后自动隐藏
            setTimeout(() => {
                statusDiv.style.display = 'none';
            }, 3000);
        }

        // 测试瓦片服务
        async function testTileService() {
            try {
                // 测试一个具体的瓦片请求
                const testUrl = 'http://localhost:3000/Tile.ashx?x=6&y=4&z=13&cm=1&dm=0&dpt=1';
                const response = await fetch(testUrl);
                
                if (response.ok) {
                    const blob = await response.blob();
                    showStatus(`瓦片服务测试成功！返回了 ${blob.size} 字节的图片数据`);
                } else {
                    showStatus(`瓦片服务测试失败：HTTP ${response.status}`, true);
                }
            } catch (error) {
                showStatus(`瓦片服务测试失败：${error.message}`, true);
            }
        }

        // 健康检查
        async function testHealthCheck() {
            try {
                const response = await fetch('http://localhost:3000/health');
                const data = await response.json();
                
                if (response.ok && data.status === 'ok') {
                    showStatus(`健康检查通过！服务状态：${data.status}`);
                } else {
                    showStatus('健康检查失败', true);
                }
            } catch (error) {
                showStatus(`健康检查失败：${error.message}`, true);
            }
        }

        // 获取服务信息
        async function testServiceInfo() {
            try {
                const response = await fetch('http://localhost:3000/info');
                const data = await response.json();
                
                if (response.ok) {
                    showStatus(`服务信息获取成功！版本：${data.version}，描述：${data.description}`);
                } else {
                    showStatus('获取服务信息失败', true);
                }
            } catch (error) {
                showStatus(`获取服务信息失败：${error.message}`, true);
            }
        }

        // 刷新地图
        function refreshMap() {
            map.eachLayer(function(layer) {
                if (layer instanceof L.TileLayer) {
                    layer.redraw();
                }
            });
            showStatus('地图已刷新');
        }

        // 地图事件监听
        map.on('tileload', function(e) {
            console.log('瓦片加载成功:', e.url);
        });

        map.on('tileerror', function(e) {
            console.error('瓦片加载失败:', e.url);
        });

        // 显示当前地图中心和缩放级别
        map.on('moveend zoomend', function() {
            const center = map.getCenter();
            const zoom = map.getZoom();
            console.log(`地图中心: ${center.lat.toFixed(5)}, ${center.lng.toFixed(5)}, 缩放级别: ${zoom}`);
        });
    </script>
</body>
</html>
