{"server": {"port": 3000, "host": "localhost"}, "logging": {"level": "info"}, "cache": {"folder": "./mapcache", "enabled": true}, "mapServices": {"DTMapService": "http://************:8002/testmapservice?service=wmts&request=gettile&tilematrixset=dt&format=image/png&layer=default&style=default&version=1.0.0&tilematrix={z}&tilerow={y}&tilecol={x}", "AEMapService": "", "SeaMapService": ""}, "mapFolders": {"landMapFolder": "/opt/ltxy/services/map-service-dotnetcore/landmap", "seaMapFolder": "/opt/ltxy/services/map-service-dotnetcore/seamap"}, "settings": {"openAllMap": true, "seaMapMode": "Standard", "showSafeLine": true, "seaMapDisplayScaleFactor": 10, "symbolBorder": false, "seaMapLayerVisible": "11060:false"}}