# Node.js 瓦片服务

这是一个用 Node.js Express 实现的地图瓦片服务，复刻了 .NET 版本的功能。

## 功能特性

- 🗺️ 兼容 Leaflet 的瓦片服务 API
- 📁 智能缓存文件查找，支持多种路径结构
- 🔄 外部地图服务代理
- 🎯 精确的瓦片坐标匹配
- 📊 健康检查和服务信息接口
- 🌐 CORS 支持
- 📝 详细的日志记录

## 安装和运行

### 1. 安装依赖

```bash
npm install
```

### 2. 配置服务

编辑 `config.json` 文件，配置您的地图服务和缓存路径：

```json
{
  "server": {
    "port": 3000,
    "host": "localhost"
  },
  "cache": {
    "folder": "./mapcache",
    "enabled": true
  },
  "mapServices": {
    "DTMapService": "http://your-map-service-url/..."
  }
}
```

### 3. 启动服务

```bash
# 生产环境
npm start

# 开发环境（自动重启）
npm run dev
```

### 4. 测试服务

打开浏览器访问：
- 测试页面: http://localhost:3000
- 健康检查: http://localhost:3000/health
- 服务信息: http://localhost:3000/info

## API 接口

### 瓦片请求

```
GET /Tile.ashx?x={x}&y={y}&z={z}&cm={cm}&dm={dm}&dpt={dpt}
```

参数说明：
- `x`: 瓦片列号
- `y`: 瓦片行号  
- `z`: 缩放级别
- `cm`: 地图模式参数（默认：1）
- `dm`: 显示模式参数（默认：0）
- `dpt`: 深度参数（默认：1）

### 健康检查

```
GET /health
```

返回服务状态信息。

### 服务信息

```
GET /info
```

返回服务详细信息和配置。

## 缓存机制

服务支持多种缓存文件查找策略：

1. **精确匹配**: `mapcache/z/x/y/z-x-y-cmdmdpt.png`
2. **文件名变体**: 支持多种文件名格式
3. **智能扫描**: 扫描可能的文件夹结构
4. **组合ID**: 支持复杂的ID组合路径

## 与 .NET 版本的兼容性

- ✅ 相同的 API 接口 (`/Tile.ashx`)
- ✅ 相同的参数格式
- ✅ 兼容的缓存文件结构
- ✅ 相同的瓦片坐标系统

## 开发和调试

### 日志

服务会输出详细的日志信息，包括：
- 请求参数
- 缓存查找过程
- 文件路径匹配
- 错误信息

### 测试

```bash
npm test
```

### 目录结构

```
├── server.js          # 主服务文件
├── config.json        # 配置文件
├── package.json       # 项目配置
├── public/            # 静态文件
│   └── index.html     # 测试页面
├── mapcache/          # 瓦片缓存目录
└── README.md          # 说明文档
```

## 故障排除

### 瓦片无法加载

1. 检查缓存文件夹路径是否正确
2. 确认瓦片文件存在且可读
3. 查看服务器日志了解详细错误信息

### 服务无法启动

1. 确认端口 3000 未被占用
2. 检查 Node.js 版本（建议 16+）
3. 确认所有依赖已正确安装

### CORS 错误

服务已启用 CORS 支持，如果仍有问题，请检查浏览器控制台错误信息。

## 许可证

MIT License
