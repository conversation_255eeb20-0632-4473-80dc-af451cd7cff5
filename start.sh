#!/bin/bash

echo "==================================="
echo "Node.js 瓦片服务启动脚本"
echo "==================================="

# 检查 Node.js 是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

# 检查 npm 是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装 npm"
    exit 1
fi

echo "✅ Node.js 版本: $(node --version)"
echo "✅ npm 版本: $(npm --version)"

# 检查是否存在 package.json
if [ ! -f "package.json" ]; then
    echo "❌ 未找到 package.json 文件"
    exit 1
fi

# 检查是否已安装依赖
if [ ! -d "node_modules" ]; then
    echo "📦 正在安装依赖..."
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ 依赖安装失败"
        exit 1
    fi
    echo "✅ 依赖安装完成"
else
    echo "✅ 依赖已存在"
fi

# 检查配置文件
if [ ! -f "config.json" ]; then
    echo "❌ 未找到 config.json 配置文件"
    exit 1
fi

# 检查缓存文件夹
CACHE_FOLDER=$(node -p "require('./config.json').cache.folder")
if [ ! -d "$CACHE_FOLDER" ]; then
    echo "⚠️  缓存文件夹不存在: $CACHE_FOLDER"
    echo "📁 正在创建缓存文件夹..."
    mkdir -p "$CACHE_FOLDER"
fi

echo "✅ 缓存文件夹: $CACHE_FOLDER"

# 获取端口配置
PORT=$(node -p "require('./config.json').server.port")
echo "🌐 服务端口: $PORT"

# 检查端口是否被占用
if lsof -Pi :$PORT -sTCP:LISTEN -t >/dev/null ; then
    echo "⚠️  端口 $PORT 已被占用"
    echo "请修改 config.json 中的端口配置或停止占用该端口的进程"
    exit 1
fi

echo "==================================="
echo "🚀 启动瓦片服务..."
echo "==================================="

# 启动服务
npm start
