{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "AllowedHosts": "*", "ListenPort": "5000", "DTMapService": "http://************:8002/testmapservice?service=wmts&amp;request=gettile&amp;tilematrixset=dt&amp;format=image/png&amp;layer=default&amp;style=default&amp;version=1.0.0&amp;tilematrix={z}&amp;tilerow={y}&amp;tilecol={x}", "AEMapService": "", "LandMapFolder": "/opt/ltxy/services/map-service-dotnetcore/landmap", "SeaMapService": "", "SeaMapDisplayScaleFactor": "10", "SymbolBorder": "false", "SeaMapFolder": "/opt/ltxy/services/map-service-dotnetcore/seamap", "CacheFolder": "./mapcache", "OpenAllMap": "true", "SeaMapMode": "Standard", "ShowSafeLine": "true", "SeaMapLayerVisible": "11060:false"}